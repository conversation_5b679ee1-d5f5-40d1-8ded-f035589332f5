#ifndef __IF__RENDERER_QUEUE_HPP
#define __IF__RENDERER_QUEUE_HPP

// C++ standard library
#include <tuple>
#include <unordered_map>
#include <vector>

// Local includes
#include "gpu_handles.hpp"
#include "instance_data.hpp"
#include "renderables/renderables.hpp"

namespace IronFrost {
  class RendererQueue {
    private:
      using RenderableObjectWithInstanceData = std::tuple<RenderableObject, InstanceData>;

      std::unordered_map<ShaderHandle, std::vector<RenderableObjectWithInstanceData>> m_renderQueue;
    public:
      void submit(const RenderableObjectWithInstanceData& renderableObjectWithInstanceData) {
        const auto& [renderableObject, instanceData] = renderableObjectWithInstanceData;

        if (instanceData.shaderOverride.has_value()) {
          m_renderQueue[instanceData.shaderOverride.value()].push_back(renderableObjectWithInstanceData);
        } else {
          m_renderQueue[renderableObject.shaderHandle].push_back(renderableObjectWithInstanceData);
        }
      }

      void clear() {
        for (auto& [shaderHandle, queue] : m_renderQueue) {
          queue.clear();
        }    
      }

      auto begin() {
        return m_renderQueue.begin();
      }

      auto end() {
        return m_renderQueue.end();
      }

      auto size() {
        return m_renderQueue.size();
      }
  };
}

#endif
