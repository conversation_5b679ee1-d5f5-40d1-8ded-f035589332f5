#ifndef __IF__MULTIPASS_FRAMEBUFFER_HPP
#define __IF__MULTIPASS_FRAMEBUFFER_HPP

// C++ standard library
#include <array>
#include <utility>

namespace IronFrost {
  class IMultipassFramebuffer {
    private:
      int m_readIndex = 0;
      int m_writeIndex = 1;

    protected:
      std::array<FramebufferHandle, 2> m_framebuffers;

    public:
      virtual ~IMultipassFramebuffer() = default;

      void swap() {
        std::swap(m_readIndex, m_writeIndex);
      }

      const FramebufferHandle& getReadFramebuffer() const {
        return m_framebuffers[m_readIndex];
      }

      const FramebufferHandle& getWriteFramebuffer() const {
        return m_framebuffers[m_writeIndex];
      }
      
      virtual void update(float width, float height) = 0;
    };
}

#endif
