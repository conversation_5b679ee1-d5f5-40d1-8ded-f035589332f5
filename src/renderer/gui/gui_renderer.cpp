#include "gui_renderer.hpp"

// C++ standard library
#include <algorithm>
#include <iostream>
#include <string>
#include <tuple>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

// Local includes
#include "../../events/event_dispatcher.hpp"
#include "../../gui/gui.hpp"
#include "../../gui/widgets/image_widget.hpp"
#include "../../gui/widgets/label_widget.hpp"
#include "../../gui/widgets/panel_widget.hpp"
#include "../../services/service_locator.hpp"
#include "../../window/window.hpp"
#include "../renderer.hpp"
#include "../resource_manager.hpp"
#include "gui_render_context.hpp"

namespace IronFrost {
  void GUIRenderer::submitWidget(Widget& widget) {
    if (widget.isDirty()) {
      m_guiPrimitivesCache.invalidate(&widget);
      createAndCacheRenderables(widget);
      widget.markClean();
    }

    m_guiAssembler.submit(m_guiPrimitivesCache.get(&widget));
  }

  void GUIRenderer::createAndCacheRenderables(Widget& widget) {
    auto& primitivesList = m_guiPrimitivesCache.get(&widget);
    GUIPrimitiveBuilder builder(primitivesList);
    widget.build(m_renderer.getResourceManager(), builder);
  }

  GUIRenderer::GUIRenderer(IRenderer& renderer) :
    m_renderer(renderer),
    m_guiRenderContext{
      .renderer = renderer,
      .guiProjection = m_guiProjection,
      .quadMesh = m_renderer.getResourceManager().get<MeshHandle>(FallbackResources::DEFAULT_QUAD_NAME),
      .guiShader = m_renderer.getResourceManager().get<ShaderHandle>(DefaultShaders::DEFAULT_GUI_SHADER_NAME),
      .textShader = m_renderer.getResourceManager().get<ShaderHandle>(DefaultShaders::DEFAULT_TEXT_SHADER_NAME)
    },
    m_guiAssembler(m_guiRenderContext)
  {
    const auto& window = m_renderer.getWindow();
    m_guiProjection.updateProjectionMatrix(window.getWidth(), window.getHeight());

    ServiceLocator::getService<EventDispatcher>().registerListener<WindowResizeEvent>(
      [&](const WindowResizeEvent &event) {
        m_guiProjection.updateProjectionMatrix(event.getWidth(), event.getHeight());
      });
  }

  void GUIRenderer::render(GUI& gui) {
    m_guiAssembler.begin(m_guiProjection);

    gui.traverseWidgets(
      [&](Widget& widget) {
        std::cout << "Checking widget visibility: " << widget.isVisible() << " type: " << widget.getType() << std::endl;
        if (widget.isVisible()) {
          std::cout << "Submitting visible widget: " << widget.getType() << std::endl;
          submitWidget(widget);
        }
      });

    m_guiAssembler.end();
  }
}
