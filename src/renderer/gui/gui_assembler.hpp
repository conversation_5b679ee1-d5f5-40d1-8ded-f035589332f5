#ifndef __IF__GUI_ASSEMBLER_HPP
#define __IF__GUI_ASSEMBLER_HPP

// C++ standard library
#include <variant>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui_primitives.hpp"
#include "../gpu_handles.hpp"
#include "../resource_manager.hpp"
#include "gui_render_context.hpp"
#include "handlers/quad_handler.hpp"
#include "handlers/text_handler.hpp"

namespace IronFrost {
  class GUIAssembler {
    private:
      GUIRenderContext& m_guiRenderContext;

      void handle(const GUIPrimitives::Quad& quad) {
        QuadHandler quadHandler(m_guiRenderContext);
        quadHandler(quad);
      }

      void handle(const GUIPrimitives::Text& text) {
        TextHandler textHandler(m_guiRenderContext);
        textHandler(text);
      }

      void handle(const GUIPrimitives::ClipPush& clipPush) {
        m_guiRenderContext.renderer.pushScissorRect(clipPush.rect);
      }

      void handle(const GUIPrimitives::ClipPop& clipPop) {
        m_guiRenderContext.renderer.popScissorRect();
      }

    public:
      GUIAssembler(GUIRenderContext& guiRenderContext) :
        m_guiRenderContext(guiRenderContext)
      {}

      void begin(GUIProjection& guiProjection) {
        IRenderer& renderer = m_guiRenderContext.renderer;

        renderer.clearRenderQueue();
        renderer.setViewProjection(glm::mat4(1.0F), guiProjection.getProjectionMatrix());
      }

      void submit(const GUIPrimitiveList& primitiveList) {
        for (const GUIPrimitive& primitive : primitiveList) {
          std::visit([&](const auto& p) { handle(p); }, primitive);
        }
      }

      void end() {
        IRenderer& renderer = m_guiRenderContext.renderer;

        renderer.withRenderState({.depthTest = false, .cullFace = false, .blend = true}, [&]() {
          renderer.render();
        });
      }
  };
}

#endif
