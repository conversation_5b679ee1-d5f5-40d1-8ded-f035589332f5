#ifndef __IF__GUI_PRIMITIVE_BUILDER_HPP
#define __IF__GUI_PRIMITIVE_BUILDER_HPP

// C++ standard library
#include <vector>

// Local includes
#include "gui_primitives.hpp"

namespace IronFrost {
  class GUIPrimitiveBuilder {
    private:
      GUIPrimitiveList& m_primitivesList;

    public:
      GUIPrimitiveBuilder(GUIPrimitiveList& primitivesList) : 
        m_primitivesList(primitivesList) 
      {}

      void add(const GUIPrimitive& primitive) {
        m_primitivesList.push_back(primitive);
      }

      void add(GUIPrimitive&& primitive) {
        m_primitivesList.push_back(std::move(primitive));
      }

      GUIPrimitiveList& getPrimitives() {
        return m_primitivesList;
      }

      void clear() {
        m_primitivesList.clear();
      }
  };
}

#endif
