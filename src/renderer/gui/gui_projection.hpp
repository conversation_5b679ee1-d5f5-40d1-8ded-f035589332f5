#ifndef __IF__GUI_PROJECTION_HPP
#define __IF__GUI_PROJECTION_HPP

#include <glm/glm.hpp>

namespace IronFrost {
  class GUIProjection {
    private:
      glm::mat4 m_projectionMatrix{1.0f};

      float m_baseScreenWidth{3840.0};
      float m_baseScreenHeight{2160.0};

    public:
      GUIProjection() = default;
      GUIProjection(const glm::mat4& projectionMatrix) : m_projectionMatrix(projectionMatrix) {}

      const glm::mat4& getProjectionMatrix() const {
        return m_projectionMatrix;
      }

      void updateProjectionMatrix(float screenWidth, float screenHeight) {
        float scale = std::min(screenWidth / m_baseScreenWidth, screenHeight / m_baseScreenHeight);

        float effectiveWidth = screenWidth / scale; 
        float effectiveHeight = screenHeight / scale;

        float offsetX = (m_baseScreenWidth - effectiveWidth) / 2.0F;
        float offsetY = (m_baseScreenHeight - effectiveHeight) / 2.0F;

        m_projectionMatrix = glm::ortho(offsetX, offsetX + effectiveWidth, offsetY + effectiveHeight, offsetY, -1.0F, 1.0F);
      }

      float getBaseScreenWidth() const {
        return m_baseScreenWidth;
      }

      float getBaseScreenHeight() const {
        return m_baseScreenHeight;
      }
  };
}

#endif
