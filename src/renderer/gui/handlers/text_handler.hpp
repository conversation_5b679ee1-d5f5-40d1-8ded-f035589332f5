#ifndef __IF__TEXT_HANDLER_HPP
#define __IF__TEXT_HANDLER_HPP

namespace IronFrost {
  class TextHandler {
    private:
      GUIRenderContext& m_guiRenderContext;

      static glm::mat4 computeGlyphTransform(const GlyphHandle& glyph, const glm::vec2& position, const glm::vec2& scale, unsigned int lineHeight) {
        glm::vec2 glyphSize = { glyph.size.x * scale.x, glyph.size.y * scale.y };
        glm::vec2 glyphPosition = {
          position.x + (glyphSize.x / 2) + (glyph.bearing.x * scale.x),
          position.y + (glyphSize.y / 2) + ((static_cast<float>(lineHeight) - glyph.size.y) * scale.y) + ((glyph.size.y - glyph.bearing.y) * scale.y)
        };

        auto transform = glm::mat4(1.0F);
        transform = glm::translate(transform, glm::vec3(glyphPosition, 0.0F));
        transform = glm::scale(transform, glm::vec3(glyphSize, 1.0F));

        return transform;
      }

      RenderableObject createRenderableGlyph(const GUIPrimitives::Text& text, const GlyphHandle& glyph) {
        const TextureHandle textureHandle{.textureID = glyph.textureID};
        const GUIMaterial material{textureHandle};

        return RenderableObject{
          .shaderHandle = m_guiRenderContext.textShader,
          .meshHandle = m_guiRenderContext.quadMesh,
          .material = material,
          .uniforms = {
            {"color", text.color.getRGB()},
            {"alpha", text.color.getA()}
          }
        };
      }

    public:
      TextHandler(GUIRenderContext& guiRenderContext) :
        m_guiRenderContext(guiRenderContext)
      {}

      void operator()(const GUIPrimitives::Text& text) {
        glm::vec2 position = text.position;
        const glm::vec2 scale = text.size;

        for (const char& c : text.text) {
          if(c == '\n') {
            position.x = text.position.x;
            position.y += static_cast<float>(text.font.lineHeight) * scale.y;
            continue;
          }

          const GlyphHandle& glyphHandle = text.font.glyphs[c];

          glm::mat4 transform = computeGlyphTransform(glyphHandle, position, scale, static_cast<unsigned int>(text.font.lineHeight));
          RenderableObject renderableGlyph = createRenderableGlyph(text, glyphHandle);

          m_guiRenderContext.renderer.submitRenderableObject(renderableGlyph, InstanceData{.transform = transform});

          position.x += static_cast<float>(glyphHandle.advance) * scale.x;
        }
      }
  };
}

#endif
