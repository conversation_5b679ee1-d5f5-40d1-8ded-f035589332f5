#ifndef __IF__QUAD_HANDLER_HPP
#define __IF__QUAD_HANDLER_HPP

namespace IronFrost {
  class QuadHandler {
    private:
      GUIRenderContext& m_guiRenderContext;

    public:
      QuadHandler(GUIRenderContext& guiRenderContext) :
        m_guiRenderContext(guiRenderContext)
      {}

      void operator()(const GUIPrimitives::Quad& quad) {
        const IResourceManager& resourceManager = m_guiRenderContext.renderer.getResourceManager();
        const GUIMaterial material{quad.texture};

        RenderableObject renderableObject{
          .shaderHandle = m_guiRenderContext.guiShader,
          .meshHandle = m_guiRenderContext.quadMesh,
          .material = material,
          .uniforms = {
            {"color", quad.color.getRGB()},
            {"alpha", quad.color.getA()}
          }
        };

        m_guiRenderContext.renderer.submitRenderableObject(renderableObject, InstanceData{.transform = quad.transform});
      }

  };
}

#endif
