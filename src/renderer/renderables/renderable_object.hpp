#ifndef __IF__RENDERABLE_OBJECT_HPP
#define __IF__RENDERABLE_OBJECT_HPP

// C++ standard library
#include <string>
#include <unordered_map>

// Local includes
#include "../gpu_handles.hpp"
#include "../material.hpp"
#include "../shader_uniforms.hpp"

namespace IronFrost {
  using RenderableObjectID = uint32_t;
  
  struct RenderableObject {
    ShaderHandle shaderHandle;
    MeshHandle meshHandle;

    Material material;
    
    ShaderUniforms uniforms;
  };
}

#endif
