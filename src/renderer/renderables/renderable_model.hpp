#ifndef __IF__RENDERABLE_MODEL_HPP
#define __IF__RENDERABLE_MODEL_HPP

// C++ standard library
#include <vector>

// Local includes
#include "renderable_object.hpp"

namespace IronFrost {
  using RenderableModelID = uint32_t;

  struct RenderableModel {
    struct Node {
      glm::mat4 transform;
  
      std::vector<RenderableObjectID> objects;
      std::vector<Node> children;
    };

    Node root;
  };  
}

#endif
