#ifndef __IF__OPENGL_RESOURCE_MANAGER_HPP
#define __IF__OPENGL_RESOURCE_MANAGER_HPP

// C++ standard library
#include <exception>
#include <functional>
#include <memory>
#include <string>
#include <vector>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/glm.hpp>
#include <glm/gtx/string_cast.hpp>

// Local includes
#include "../../assets/asset_data_types.hpp"
#include "../gpu_handles.hpp"
#include "../resource_manager.hpp"
#include "opengl_shader.hpp"

namespace IronFrost {
  class OpenGLResourceManager : public IResourceManager {
    private:
      void unloadMeshes() {
        for (auto& [name, meshHandle] : m_resourceHandles.getContainer<MeshHandle>()) {
          destroyMesh(name);
        }
      }

      void unloadModels() {
        for (auto& [name, modelHandle] : m_resourceHandles.getContainer<ModelHandle>()) {
          destroyModel(name);
        }
      }

      void unloadTextures() {
        for (auto& [name, textureHandle] : m_resourceHandles.getContainer<TextureHandle>()) {
          destroyTexture(name);
        }
      }

      void unloadFonts() {
        for (auto& [name, fontHandle] : m_resourceHandles.getContainer<FontHandle>()) {
          destroyFont(name);
        }
      }

      void unloadShaders() {
        for (auto& [name, shaderHandle] : m_resourceHandles.getContainer<ShaderHandle>()) {
          destroyShader(name);
        }
      }

    public:
      OpenGLResourceManager() = default;

      virtual ~OpenGLResourceManager() {
        unloadMeshes();
        unloadModels();
        unloadTextures();
        unloadFonts();
        unloadShaders();
      };

      MeshHandle createMesh(const MeshData& meshData) override {
        MeshHandle meshHandle;

        glGenVertexArrays(1, &meshHandle.VAO);
        glBindVertexArray(meshHandle.VAO);
    
        unsigned int vertexVBO;
        glGenBuffers(1, &vertexVBO);
        glBindBuffer(GL_ARRAY_BUFFER, vertexVBO);
        glBufferData(GL_ARRAY_BUFFER, 
          meshData.vertices.size() * sizeof(decltype(meshData.vertices)::value_type), 
          meshData.vertices.data(), 
          GL_STATIC_DRAW);
    
        // Position
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 14 * sizeof(float), (void *)0);
        glEnableVertexAttribArray(0);
        // Tex Coords
        glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 14 * sizeof(float), (void *)(3 * sizeof(float)));
        glEnableVertexAttribArray(1);
        // Normal
        glVertexAttribPointer(2, 3, GL_FLOAT, GL_FALSE, 14 * sizeof(float), (void *)(5 * sizeof(float)));
        glEnableVertexAttribArray(2);
        // Tangent
        glVertexAttribPointer(3, 3, GL_FLOAT, GL_FALSE, 14 * sizeof(float), (void *)(8 * sizeof(float)));
        glEnableVertexAttribArray(3);
        // Bitangent
        glVertexAttribPointer(4, 3, GL_FLOAT, GL_FALSE, 14 * sizeof(float), (void *)(11 * sizeof(float)));
        glEnableVertexAttribArray(4);
    
        unsigned int EBO;
        glGenBuffers(1, &EBO);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, EBO);
        glBufferData(GL_ELEMENT_ARRAY_BUFFER,   
          meshData.indices.size() * sizeof(decltype(meshData.indices)::value_type), 
          meshData.indices.data(), 
          GL_STATIC_DRAW);
    
        glBindVertexArray(0);
        glBindBuffer(GL_ARRAY_BUFFER, 0);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);
    
        glDeleteBuffers(1, &vertexVBO);
        glDeleteBuffers(1, &EBO);
    
        meshHandle.numIndices = meshData.indices.size();
        meshHandle.numVertices = meshData.vertices.size();
    
        return meshHandle;
      }

      const MeshHandle& createMesh(const StringID& name, const MeshData& meshData) override {
        if (has<MeshHandle>(name)) {
          throw std::runtime_error("Mesh already exists: " + StringID::getString(name));
        }

        insert<MeshHandle>(name, createMesh(meshData));
        return get<MeshHandle>(name);
      }

      void destroyMesh(const MeshHandle& meshHandle) override {
        glDeleteVertexArrays(1, &meshHandle.VAO);
      }

      void destroyMesh(const StringID& name) override {
        if (!has<MeshHandle>(name)) {
          return;
        }

        destroyMesh(get<MeshHandle>(name));
        remove<MeshHandle>(name);
      }

      TextureHandle createTexture(const ImageData& _imageData) override {
        if (!_imageData.data)
          throw std::runtime_error("Texture data is null");

        if (_imageData.channels != 3 && _imageData.channels != 4) 
          throw std::runtime_error("Texture data has invalid number of channels");
  
        TextureHandle textureHandle;
    
        glGenTextures(1, &textureHandle.textureID);
        glBindTexture(GL_TEXTURE_2D, textureHandle.textureID);
    
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    
        glPixelStorei(GL_UNPACK_ALIGNMENT, 1);
    
        GLenum dataFormat = (_imageData.channels == 4) ? GL_RGBA : GL_RGB;
    
        if (_imageData.is16bit) {
          GLenum internalFormat = (_imageData.channels == 4) ? GL_RGBA16 : GL_RGB16;
          glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, _imageData.width, _imageData.height, 0, dataFormat, GL_UNSIGNED_SHORT, _imageData.get16BitData());
        } else {
          GLenum internalFormat = (_imageData.channels == 4) ? GL_RGBA8 : GL_RGB8;
          glTexImage2D(GL_TEXTURE_2D, 0, internalFormat, _imageData.width, _imageData.height, 0, dataFormat, GL_UNSIGNED_BYTE, _imageData.get8BitData());
        }
        
        glGenerateMipmap(GL_TEXTURE_2D);
    
        glBindTexture(GL_TEXTURE_2D, 0);
        glPixelStorei(GL_UNPACK_ALIGNMENT, 4);
    
        return textureHandle;
      }

      const TextureHandle& createTexture(const StringID& name, const ImageData& _imageData) override {
        if (has<TextureHandle>(name)) {
          throw std::runtime_error("Texture already exists: " + StringID::getString(name));
        }

        insert<TextureHandle>(name, createTexture(_imageData));
        return get<TextureHandle>(name);
      }

      TextureHandle createTextureArray(const std::vector<std::unique_ptr<ImageData>>& imageDataArray) override {
        if (imageDataArray.empty())
            throw std::runtime_error("Texture array data is missing");

        TextureHandle textureHandle;
        textureHandle.type = TextureType::ARRAY;

        glGenTextures(1, &textureHandle.textureID);
        glBindTexture(GL_TEXTURE_2D_ARRAY, textureHandle.textureID);

        glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
        glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_WRAP_S, GL_REPEAT);
        glTexParameteri(GL_TEXTURE_2D_ARRAY, GL_TEXTURE_WRAP_T, GL_REPEAT);

        unsigned int width = imageDataArray[0]->width;
        unsigned int height = imageDataArray[0]->height;
        int channels = imageDataArray[0]->channels;

        // Validate that all images have the same dimensions and channel count
        for (const auto& img : imageDataArray) {
          if (img->width != width || img->height != height || img->channels != channels) {
              throw std::runtime_error("All images in texture array must have the same dimensions and number of channels");
          }
        }

        GLenum internalFormat = (channels == 4) ? GL_RGBA8 : GL_RGB8;
        GLenum format = (channels == 4) ? GL_RGBA : GL_RGB;

        glTexImage3D(GL_TEXTURE_2D_ARRAY, 0, internalFormat, width, height, imageDataArray.size(), 0, format, GL_UNSIGNED_BYTE, nullptr);
        glPixelStorei(GL_UNPACK_ALIGNMENT, 1);

        int layerIndex = 0;
        for (auto& imageData : imageDataArray) {
            glTexSubImage3D(GL_TEXTURE_2D_ARRAY, 0, 0, 0, layerIndex, width, height, 1, format, GL_UNSIGNED_BYTE, imageData->get8BitData());
            ++layerIndex;
        }

        glGenerateMipmap(GL_TEXTURE_2D_ARRAY);

        glBindTexture(GL_TEXTURE_2D_ARRAY, 0);
        glPixelStorei(GL_UNPACK_ALIGNMENT, 4);

        return textureHandle;
      }

      const TextureHandle& createTextureArray(const StringID& name, const std::vector<std::unique_ptr<ImageData>>& imageDataArray) override {
        if (has<TextureHandle>(name)) {
          throw std::runtime_error("Texture already exists: " + StringID::getString(name));
        }

        insert<TextureHandle>(name, createTextureArray(imageDataArray));
        return get<TextureHandle>(name);
      }

      void destroyTexture(const TextureHandle& textureHandle) override {
        glDeleteTextures(1, &textureHandle.textureID);
      }

      void destroyTexture(const StringID& name) override {
        if (!has<TextureHandle>(name)) {
          return;
        }

        destroyTexture(get<TextureHandle>(name));
        remove<TextureHandle>(name);
      }

      const ModelHandle& createModel(const StringID& name, const ModelData& modelData) override {
        if (has<ModelHandle>(name)) {
          throw std::runtime_error("Model already exists: " + StringID::getString(name));
        }

        ModelHandle modelHandle;

        std::function<ModelHandle::Node(const ModelDataNode&)> processNode = [&](const ModelDataNode& modelDataNode) {
          ModelHandle::Node modelHandleNode;
          modelHandleNode.transform = modelDataNode.transform;
          
          for (const auto& meshData : modelDataNode.meshes) {
            modelHandleNode.meshes.push_back(createMesh(*meshData));
          }

          for (const auto& textureData : modelDataNode.textures) {
            if (textureData) {
              modelHandleNode.textures.push_back(createTexture(*textureData));
            } else {
              modelHandleNode.textures.push_back(TextureHandle{0});
            }
          }

          for (auto& childNode : modelDataNode.children) {
            modelHandleNode.children.push_back(processNode(childNode));
          }

          return modelHandleNode;
        };

        modelHandle.rootNode = processNode(modelData.rootNode);

        insert<ModelHandle>(name, modelHandle);
        return get<ModelHandle>(name);
      }

      void destroyModel(const ModelHandle& modelHandle) override {
        std::function<void(const ModelHandle::Node&)> destroyNode = [&](const ModelHandle::Node& modelHandleNode) {
          for (const auto& meshHandle : modelHandleNode.meshes) {
            destroyMesh(meshHandle);
          }

          for (const auto& textureHandle : modelHandleNode.textures) {
            destroyTexture(textureHandle);
          }

          for (auto& childNode : modelHandleNode.children) {
            destroyNode(childNode);
          }
        };

        destroyNode(modelHandle.rootNode);
      }

      void destroyModel(const StringID& name) override {
        if (!has<ModelHandle>(name)) {
          return;
        }

        destroyModel(get<ModelHandle>(name));
        remove<ModelHandle>(name);
      }

      MeshHandle createTerrain(const StringID& name, const HeightmapData& heightmapData) override {
        MeshHandle meshHandle;

        insert<MeshHandle>(name, meshHandle);

        return get<MeshHandle>(name);
      }

      void destroyTerrain(const StringID& name) override {
        remove<MeshHandle>(name);
      }

      const FontHandle& createFont(const StringID& name, const FontData& fontData) override {
        if (has<FontHandle>(name)) {
          throw std::runtime_error("Font already exists: " + StringID::getString(name));
        }

        FontHandle fontHandle;
        fontHandle.lineHeight = fontData.lineHeight;
    
        for(auto index = 0; index < fontData.glyphs.size(); ++index) {
          auto &c = fontData.glyphs[index];
          unsigned int textureID;
    
          glGenTextures(1, &textureID);
          glBindTexture(GL_TEXTURE_2D, textureID);
    
          glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
          glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
          glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
          glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    
          glPixelStorei(GL_UNPACK_ALIGNMENT, 1);
    
          glTexImage2D(GL_TEXTURE_2D, 0, GL_RED, c.size.x, c.size.y, 0, GL_RED, GL_UNSIGNED_BYTE, c.bitmap.data());
          glGenerateMipmap(GL_TEXTURE_2D);
    
          fontHandle.glyphs[index] = { textureID, c.size, c.bearing, c.advance };
        }
    
        glBindTexture(GL_TEXTURE_2D, 0);
    
        insert<FontHandle>(name, fontHandle);
        return get<FontHandle>(name);
      }

      void destroyFont(const FontHandle& fontHandle) override {
        for(auto& glyphHandle : fontHandle.glyphs) {
          glDeleteTextures(1, &glyphHandle.textureID);
        }
      }

      void destroyFont(const StringID& name) override {
        if (!has<FontHandle>(name)) {
          return;
        }

        destroyFont(get<FontHandle>(name));
        remove<FontHandle>(name);
      }

      const ShaderHandle& createShader(const StringID& name, const std::string& vertexShaderSource, const std::string& fragmentShaderSource) override {
        if (has<ShaderHandle>(name)) {
          throw std::runtime_error("Shader already exists: " + StringID::getString(name));
        }

        OpenGLShader shader(vertexShaderSource, fragmentShaderSource);
        ShaderHandle shaderHandle = {shader.build()};
    
        shader.activeUniforms(
          [&](const std::string& name, int uniformLocation, unsigned int uniformType) {
            shaderHandle.activeUniforms.emplace(name, std::make_tuple(uniformLocation, uniformType));
          });
    
        insert<ShaderHandle>(name, shaderHandle);
        return get<ShaderHandle>(name);
      }

      void destroyShader(const ShaderHandle& shaderHandle) override {
        glDeleteProgram(shaderHandle.programID);
      }

      void destroyShader(const StringID& name) override {
        if (!has<ShaderHandle>(name)) {
          return;
        }

        destroyShader(get<ShaderHandle>(name));
        remove<ShaderHandle>(name);
      }
  };
}

#endif
