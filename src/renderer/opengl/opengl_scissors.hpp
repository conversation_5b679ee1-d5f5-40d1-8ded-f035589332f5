#ifndef __IF__OPENGL_SCISSOR_STACK_HPP
#define __IF__OPENGL_SCISSOR_STACK_HPP

// C++ standard library
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../pixel_rect.hpp"

namespace IronFrost {
  class OpenGLScissors {
    private:
      std::vector<PixelRect> m_scissorStack;

      bool m_savedScissorEnabled{false};
      GLint m_savedScissorBox[4]{0,0,0,0};

    public:

      void push(PixelRect rect) {
        GLint viewport[4];
        glGetIntegerv(GL_VIEWPORT, viewport);

        rect = PixelRect::clampToViewport(rect, {viewport[0], viewport[1], viewport[2], viewport[3]});

        if (m_scissorStack.empty()) {
          m_savedScissorEnabled = glIsEnabled(GL_SCISSOR_TEST);
          glGetIntegerv(GL_SCISSOR_BOX, m_savedScissorBox);
        } else {
          rect = PixelRect::intersect(rect, m_scissorStack.back());
        }

        m_scissorStack.push_back(rect);

        glScissor(rect.x, rect.y, std::max(0, rect.width), std::max(0, rect.height));
        glEnable(GL_SCISSOR_TEST);
      }

      void pop() {
        if (m_scissorStack.empty()) {
          return;
        }

        m_scissorStack.pop_back();

        if (m_scissorStack.empty()) {
          if (m_savedScissorEnabled) {
            glEnable(GL_SCISSOR_TEST);
            glScissor(m_savedScissorBox[0], m_savedScissorBox[1], m_savedScissorBox[2], m_savedScissorBox[3]);
          } else {
            glDisable(GL_SCISSOR_TEST);
          }
        } else {
          const auto& rect = m_scissorStack.back();
          glEnable(GL_SCISSOR_TEST);
          glScissor(rect.x, rect.y, rect.width, rect.height);
        }
      }
  };
}

#endif
