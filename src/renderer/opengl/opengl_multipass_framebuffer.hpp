#ifndef __IF__OPENGL_MULTIPASS_FRAMEBUFFER_HPP
#define __IF__OPENGL_MULTIPASS_FRAMEBUFFER_HPP

// C++ standard library
#include <iostream>

// Local includes
#include "../multipass_framebuffer.hpp"

namespace IronFrost {
  class OpenGLMultipassFramebuffer : public IMultipassFramebuffer {
    private:
      FramebufferHandle createFramebuffer(float width, float height) {
        FramebufferHandle framebufferHandle;

        glGenFramebuffers(1, &framebufferHandle.framebufferID);
        glBindFramebuffer(GL_FRAMEBUFFER, framebufferHandle.framebufferID);

        glGenTextures(1, &framebufferHandle.textureID);
        glBindTexture(GL_TEXTURE_2D, framebufferHandle.textureID);

        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);

        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, width, height, 0, GL_RGB, GL_UNSIGNED_BYTE, nullptr);
        glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, framebufferHandle.textureID, 0);

        unsigned int RBO;
        glGenRenderbuffers(1, &RBO);
        glBindRenderbuffer(GL_RENDERBUFFER, RBO);
        glRenderbufferStorage(GL_RENDERBUFFER, GL_DEPTH24_STENCIL8, width, height);
        glBindRenderbuffer(GL_RENDERBUFFER, 0);

        glFramebufferRenderbuffer(GL_FRAMEBUFFER, GL_DEPTH_STENCIL_ATTACHMENT, GL_RENDERBUFFER, RBO);

        if(glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE) {
          std::cerr << "Framebuffer is not complete!" << std::endl;
        }

        glBindFramebuffer(GL_FRAMEBUFFER, 0);

        return framebufferHandle;
      }

      void createFramebuffers(float width, float height) {
        for (int i = 0; i < 2; i++) {
          m_framebuffers[i] = createFramebuffer(width, height);
        }
      }

      void deleteFramebuffers() {
        for (int i = 0; i < 2; i++) {
          glDeleteFramebuffers(1, &m_framebuffers[i].framebufferID);
          glDeleteTextures(1, &m_framebuffers[i].textureID);
        }
      }
    public:
      OpenGLMultipassFramebuffer(float width, float height) : IMultipassFramebuffer() {
        createFramebuffers(width, height);
      }

      ~OpenGLMultipassFramebuffer() {
        deleteFramebuffers();
      }

      void update(float width, float height) override {
        deleteFramebuffers();
        createFramebuffers(width, height);
      }
  };
}

#endif
