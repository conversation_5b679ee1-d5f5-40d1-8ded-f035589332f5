#ifndef __IF__GUI_LOADER_HPP
#define __IF__GUI_LOADER_HPP

// C++ standard library
#include <memory>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "../../gui/widgets/image_widget.hpp"
#include "../../gui/widgets/label_widget.hpp"
#include "../../gui/widgets/panel_widget.hpp"

using json = nlohmann::json;

namespace IronFrost {
  class GUILoader {
    private:
      std::unique_ptr<ImageWidget> loadImageWidget(json widgetData) const {
        std::unique_ptr<ImageWidget> newWidget = std::make_unique<ImageWidget>(StringID(widgetData["texture"]));

        if(widgetData.contains("color")) {
          glm::vec4 color = widgetData.value("color", glm::vec4(1.0F, 1.0F, 1.0F, 1.0F));

          newWidget->setColor(color.rgb());
          newWidget->setAlpha(color.a);
        }

        return newWidget;
      }

      std::unique_ptr<LabelWidget> loadLabelWidget(json widgetData) const {
        std::unique_ptr<LabelWidget> newWidget = std::make_unique<LabelWidget>(StringID(widgetData["font"]), widgetData["label"]);

        if(widgetData.contains("color")) {
          glm::vec4 color = widgetData.value("color", glm::vec4(1.0F, 1.0F, 1.0F, 1.0F));

          newWidget->setColor(color.rgb());
          newWidget->setAlpha(color.a);
        }

        return newWidget;
      }

      std::unique_ptr<PanelWidget> loadPanelWidget(json widgetData) const {
        std::unique_ptr<PanelWidget> newWidget = std::make_unique<PanelWidget>();

        if(widgetData.contains("children")) {
          for (const auto& child : widgetData["children"]) {
            StringID childName = StringID(child["name"]);
            std::unique_ptr<Widget> childWidget = loadWidget(child);

            newWidget->addWidget(childName, std::move(childWidget));
          }
        }

        return newWidget;
      }

      std::unique_ptr<Widget> loadWidget(json widgetData) const {
        std::unique_ptr<Widget> newWidget;
        std::string type = widgetData["type"].get<std::string>();

        if(type == "image") {
          newWidget = loadImageWidget(widgetData);
        } else if(type == "label") {
          newWidget = loadLabelWidget(widgetData);
        } else if(type == "panel") {
          newWidget = loadPanelWidget(widgetData);
        } else {
          throw std::runtime_error("Unknown widget type: " + type);
        }

        return newWidget;
      }

    public:
      GUILoader() = default;
      ~GUILoader() = default;

      void loadWidgets(json widgets, const std::function<void(const StringID name, std::unique_ptr<Widget> widget)>& callback) const {
        for (const auto& widget : widgets) {
          StringID name = StringID(widget["name"]);

          glm::vec2 position = widget.value("position", glm::vec2(0.0F, 0.0F));
          glm::vec2 size = widget.value("size", glm::vec2(1.0F, 1.0F));

          std::unique_ptr<Widget> newWidget = loadWidget(widget);

          newWidget->setPosition(position);
          newWidget->setSize(size);

          callback(name, std::move(newWidget));
        }
      }
  };
}

#endif
