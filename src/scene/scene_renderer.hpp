#ifndef __IF__SCENE_RENDERER_HPP
#define __IF__SCENE_RENDERER_HPP

// C++ standard library
#include <iostream>
#include <memory>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/glm.hpp>
#include <glm/gtx/string_cast.hpp>

// Local includes
#include "../gui/gui.hpp"
#include "../renderer/gui/gui_renderer.hpp"
#include "../renderer/instance_data.hpp"
#include "../renderer/postprocess_effect.hpp"
#include "../renderer/renderables_manager.hpp"
#include "../renderer/renderer.hpp"
#include "camera/camera.hpp"
#include "debug/scene_debug_renderer.hpp"
#include "scene_graph/scene_graph.hpp"

namespace IronFrost {
  class SceneRenderer {
    private:
      IRenderer& m_renderer;
      std::unique_ptr<SceneDebugRenderer> m_debugRenderer;

      PostprocessEffect& m_postprocessEffect;
      ShaderUniforms m_postprocessUniforms{};

      void submitRenderableModel(RenderableModelID modelID, const glm::mat4& transform) {
        const RenderablesManager& manager = m_renderer.getRenderablesManager();
        const RenderableModel* model = manager.getRenderableModel(modelID);

        if (!model) return;

        submitRenderableModelNode(model->root, transform);
      }

      void submitRenderableModelNode(const RenderableModel::Node& node, const glm::mat4& transform) {
        glm::mat4 instanceTransform = transform * node.transform;

        for (const RenderableObjectID& objectID : node.objects) {
          m_renderer.submitRenderableObject(objectID, InstanceData{instanceTransform});
        }

        for (const RenderableModel::Node& child : node.children) {
          submitRenderableModelNode(child, instanceTransform);
        }
      }

      void submitSceneObject(const SceneObject& object, const glm::mat4& transform) {
        m_renderer.submitRenderableObject(
          object.renderableObjectID,
          InstanceData{
            .transform = transform,
            .uvTiling = object.uvTiling}
        );
      }

    public:
      SceneRenderer(IRenderer& renderer) :
        m_renderer(renderer),
        m_debugRenderer(std::make_unique<SceneDebugRenderer>(renderer)),
        m_postprocessEffect(m_renderer.getPostprocessEffect(StringID("postprocess::default")))
      {}

      void setPostprocessUniforms(const ShaderUniforms& uniforms) {
        m_postprocessUniforms = uniforms;
        m_postprocessEffect.uniforms = m_postprocessUniforms;
      }

      void setPostprocessEffect(PostprocessEffect& postprocessEffect) {
        m_postprocessEffect = postprocessEffect;
        m_postprocessEffect.uniforms = m_postprocessUniforms;
      }

      void setPostprocessEffect(StringID effectName) {
        setPostprocessEffect(m_renderer.getPostprocessEffect(effectName));
      }

      void processScene(ISceneGraph& sceneGraph, ISpatialPartitioning& spatialPartitioning, Camera& camera) {
        m_renderer.clearRenderQueue();
        m_renderer.setViewProjection(camera.getViewMatrix(), camera.getProjectionMatrix());

        Frustum frustum = camera.getFrustum();

        spatialPartitioning.collectVisible(frustum, [&](SceneNode* node, const glm::mat4& transform) {
          if (!node->visible) return;

          if (node->sceneObject) {
            submitSceneObject(node->sceneObject.value(), transform);
          }

          if (node->sceneModel) {
            submitRenderableModel(node->sceneModel->renderableModelID, transform);
          }
        });

        sceneGraph.traverse([&](const SceneNode& node, const glm::mat4& transform) {
          if (!node.visible) return;

          if (node.sceneLight) {
            RenderableLight light{
              RenderableLight::Type::Point, 
              node.getPosition(),
              node.sceneLight->color, 
              node.sceneLight->intensity, 
              node.sceneLight->constant, 
              node.sceneLight->linear, 
              node.sceneLight->quadratic
            };

            m_renderer.submitRenderableLight(light);
          }
        });
      };

      void render(ISceneGraph& sceneGraph, ISpatialPartitioning& spatialPartitioning, Camera& camera, GUI& gui) {
        m_renderer.renderToFramebuffer([&]() {
          processScene(sceneGraph, spatialPartitioning, camera);

          m_renderer.withRenderState({.depthTest = true, .cullFace = true, .blend = true}, [&]() {
            m_renderer.render();
          });

          m_debugRenderer->render(sceneGraph, camera);
        });

        m_renderer.renderPostprocessEffect(m_postprocessEffect);
        m_renderer.getGUIRenderer().render(gui);
      }
  };
}

#endif
