#ifndef __IF__COLLISION_SHAPE_HPP
#define __IF__COLLISION_SHAPE_HPP

// Local includes
#include "../../../utils/collision_math.hpp"

namespace IronFrost {
  class CollisionAABB;
  class CollisionSphere;
  class CollisionPlane;
  class CollisionTerrain;
  
  class CollisionShape {
    public:
      virtual ~CollisionShape() = default;

      virtual void update(const glm::mat4& transform) = 0;

      virtual CollisionMath::AABB getWorldAABB() const = 0;

      virtual bool intersects(const CollisionShape& other) const = 0;
      virtual bool intersectsSphere(const CollisionSphere& sphere) const = 0;
      virtual bool intersectsAABB(const CollisionAABB& aabb) const = 0;
      virtual bool intersectsPlane(const CollisionPlane& plane) const = 0;
      virtual bool intersectsTerrain(const CollisionTerrain& terrain) const = 0;

      virtual std::optional<CollisionMath::SweepHit> sweepSphere(const CollisionMath::Sphere& sphere, const glm::vec3& delta) const {
        return std::nullopt;
      }

      virtual glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) {
        return sphere.center;
      }

      virtual glm::vec3 resolveAABBCollision(const CollisionMath::AABB& aabb) {
        return aabb.getCenter();
      }
  };
}

#endif
