#ifndef __IF__IMAGE_WIDGET_HPP
#define __IF__IMAGE_WIDGET_HPP

// C++ standard library
#include <string>

// Local includes
#include "../../renderer/fallback_resources.hpp"
#include "../../renderer/gui/gui_primitives.hpp"
#include "../../renderer/resource_manager.hpp"
#include "mixins/colorable.hpp"
#include "widget.hpp"

namespace IronFrost {
  class ImageWidget : public Widget, public Colorable {
    private:
      StringID m_textureName;

    public:
      ImageWidget() :
        Widget(),
        m_textureName(FallbackResources::FALLBACK_TEXTURE_NAME)
      {}

      explicit ImageWidget(const StringID& textureName) :
        Widget(),
        m_textureName(textureName)
      {}

      ImageWidget(glm::vec2 position, glm::vec2 size, const StringID& textureName) :
        Widget(position, size),
        m_textureName(textureName)
      {}

      void setTextureName(const StringID& textureName) {
        m_textureName = textureName;
        markDirty();
      }

      const StringID& getTextureName() const {
        return m_textureName;
      }

      std::string getType() const override {
        return "image";
      }

      void build(IResourceManager& resourceManager,GUIPrimitiveBuilder& builder) override {
        builder.add(GUIPrimitives::Quad{
          .transform = getTransform(),
          .uv = glm::vec4(0.0f, 0.0f, 1.0f, 1.0f),
          .texture = resourceManager.get<TextureHandle>(m_textureName),
          .color = getColor()
        });
      }
  };
}

#endif
