#ifndef __IF__PANEL_WIDGET_HPP
#define __IF__PANEL_WIDGET_HPP

// C++ standard library
#include <exception>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>

// Local includes
#include "../../renderer/fallback_resources.hpp"
#include "../../renderer/gui/gui_primitives.hpp"
#include "../../renderer/pixel_rect.hpp"
#include "mixins/colorable.hpp"
#include "widget.hpp"

namespace IronFrost {
  class PanelWidget : public Widget, public Colorable {
    private:
      std::unordered_map<StringID, std::unique_ptr<Widget>> m_widgets;
      bool m_renderBackground{false};

    public: 
      PanelWidget() : 
        Widget() 
      {} 

      PanelWidget(glm::vec2 _position, glm::vec2 _size) :
        Widget(_position, _size)
      {}

      bool isDirty() const override {
        bool dirty = Widget::isDirty();
        for (auto& [id, widget] : m_widgets) {
          dirty |= widget->isDirty();
        }
        return dirty;
      }

      void markDirty() override {
        Widget::markDirty();
        for (auto& [id, widget] : m_widgets) {
          widget->markDirty();
        }
      }

      void markClean() override {
        Widget::markClean();
        for (auto& [id, widget] : m_widgets) {
          widget->markClean();
        }
      }

      void update(float _deltaTime, GameContext& _gameContext) override {
        if (!isVisible()) return;

        Widget::update(_deltaTime, _gameContext);
        for (auto& [id, widget] : m_widgets) {
          widget->update(_deltaTime, _gameContext);
        }
      }

      void addWidget(const StringID& _id, std::unique_ptr<Widget> _widget) {
        m_widgets.try_emplace(_id, std::move(_widget));
        markDirty();
      }

      void addWidgetRaw(const StringID& _id, Widget* _widget) {
        m_widgets.try_emplace(_id, std::unique_ptr<Widget>(_widget));
        markDirty();
      }

      void removeWidget(const StringID& _id) {
        m_widgets.erase(_id);
        markDirty();
      }

      Widget& getWidget(const StringID& _id) const {
        auto it = m_widgets.find(_id);
        if (it != m_widgets.end()) {
            return *it->second.get();
        }
        throw std::runtime_error("Widget not found");
      }

      void traverseWidgets(std::function<void(Widget&)> _func) {
        for (auto& [id, widget] : m_widgets) {
          _func(*widget);
        }
      }

      std::string getType() const override {
        return "panel";
      }

      void setRenderBackground(bool renderBackground) {
        m_renderBackground = renderBackground;
        markDirty();
      }

      bool getRenderBackground() const {
        return m_renderBackground;
      }

      void build(IResourceManager& resourceManager, GUIPrimitiveBuilder& builder) override {
        std::cout << "XXXXXXXXXXX Building panel widget" << std::endl;

        const auto& panelPos = getPosition();
        const auto& panelSize = getSize();

        // Render background if enabled
        if (m_renderBackground) {
          builder.add(GUIPrimitives::Quad{
            .transform = getTransform(),
            .uv = glm::vec4(0.0f, 0.0f, 1.0f, 1.0f),
            .texture = resourceManager.get<TextureHandle>(FallbackResources::DEFAULT_TEXTURE_NAME),
            .color = getColor()
          });
        }

        // Add clipping to constrain children to panel bounds
        builder.add(GUIPrimitives::ClipPush{
          .rect = PixelRect{
            static_cast<int>(panelPos.x),
            static_cast<int>(panelPos.y),
            static_cast<int>(panelSize.x),
            static_cast<int>(panelSize.y)
          }
        });

        for (auto& [id, widget] : m_widgets) {
          if (!widget->isVisible()) {
            continue;
          }

          std::cout << "YYYYYYYYYYYY Building child widget: " << id << std::endl;

          // Transform child position to be relative to panel
          const auto& childPos = widget->getPosition();
          const auto& childSize = widget->getSize();
          const float childRotation = widget->getRotation();

          // Store original child transform
          const auto originalPos = childPos;
          const auto originalSize = childSize;
          const auto originalRotation = childRotation;

          // Set child position relative to panel
          widget->setPosition(panelPos + childPos);

          // Build the child widget
          widget->build(resourceManager, builder);

          // Restore original child transform
          widget->setPosition(originalPos);
        }

        // Pop clipping
        builder.add(GUIPrimitives::ClipPop{});
      }

      void traverse(std::function<void(Widget&)> func) override {
        func(*this);
        for (auto& [id, widget] : m_widgets) {
          widget->traverse(func);
        }
      }
  };
}

#endif
