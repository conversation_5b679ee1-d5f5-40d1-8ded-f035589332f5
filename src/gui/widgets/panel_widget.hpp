#ifndef __IF__PANEL_WIDGET_HPP
#define __IF__PANEL_WIDGET_HPP

// C++ standard library
#include <exception>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>

// Local includes
#include "widget.hpp"

namespace IronFrost {
  class PanelWidget : public Widget {
    private:
      std::unordered_map<StringID, std::unique_ptr<Widget>> m_widgets;

    public: 
      PanelWidget() : 
        Widget() 
      {} 

      PanelWidget(glm::vec2 _position, glm::vec2 _size) :
        Widget(_position, _size)
      {}

      bool isDirty() const override {
        bool dirty = Widget::isDirty();
        for (auto& [id, widget] : m_widgets) {
          dirty |= widget->isDirty();
        }
        return dirty;
      }

      void markDirty() override {
        Widget::markDirty();
        for (auto& [id, widget] : m_widgets) {
          widget->markDirty();
        }
      }

      void markClean() override {
        Widget::markClean();
        for (auto& [id, widget] : m_widgets) {
          widget->markClean();
        }
      }

      void update(float _deltaTime, GameContext& _gameContext) override {
        if (!isVisible()) return;

        Widget::update(_deltaTime, _gameContext);
        for (auto& [id, widget] : m_widgets) {
          widget->update(_deltaTime, _gameContext);
        }
      }

      void addWidget(const StringID& _id, std::unique_ptr<Widget> _widget) {
        m_widgets.try_emplace(_id, std::move(_widget));
        markDirty();
      }

      void addWidgetRaw(const StringID& _id, Widget* _widget) {
        m_widgets.try_emplace(_id, std::unique_ptr<Widget>(_widget));
        markDirty();
      }

      void removeWidget(const StringID& _id) {
        m_widgets.erase(_id);
        markDirty();
      }

      Widget& getWidget(const StringID& _id) const {
        auto it = m_widgets.find(_id);
        if (it != m_widgets.end()) {
            return *it->second.get();
        }
        throw std::runtime_error("Widget not found");
      }

      void traverseWidgets(std::function<void(Widget&)> _func) {
        for (auto& [id, widget] : m_widgets) {
          _func(*widget);
        }
      }

      std::string getType() const override {
        return "panel";
      }

      void build(IResourceManager& resourceManager, GUIPrimitiveBuilder& builder) override {
        std::cout << "XXXXXXXXXXX Building panel widget" << std::endl;

        for (auto& [id, widget] : m_widgets) {
          if (!widget->isVisible()) {
            continue;
          }

          std::cout << "YYYYYYYYYYYY Building child widget: " << id << std::endl;
          widget->build(resourceManager, builder);
        }
      }

      void traverse(std::function<void(Widget&)> func) override {
        func(*this);
        for (auto& [id, widget] : m_widgets) {
          widget->traverse(func);
        }
      }
  };
}

#endif
