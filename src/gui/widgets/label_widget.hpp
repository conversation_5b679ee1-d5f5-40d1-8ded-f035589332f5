#ifndef __IF__LABEL_WIDGET_HPP
#define __IF__LABEL_WIDGET_HPP

// C++ standard library
#include <iostream>
#include <string>

// Local includes
#include "mixins/colorable.hpp"
#include "widget.hpp"

namespace IronFrost {
  class LabelWidget : public Widget, public Colorable {
    private:
      StringID m_font;
      std::string m_text{""};

    public:
      LabelWidget(StringID _font, const std::string& _text) : 
        Widget(),
        m_font(_font),
        m_text(_text) 
      {}

      LabelWidget(glm::vec2 _position, glm::vec2 _size, StringID _font, const std::string& _text) :
        Widget(_position, _size),
        m_font(_font),
        m_text(_text)
      {}

      void setText(const std::string& _text) {
        m_text = _text;
        markDirty();
      }

      const std::string& getText() const {
        return m_text;
      }

      void setFontName(StringID _font) {
        m_font = _font;
        markDirty();
      }

      const StringID getFontName() const {
        return m_font;
      }

      std::string getType() const override {
        return "label";
      }

      void build(IResourceManager& resourceManager, GUIPrimitiveBuilder& builder) override {
        builder.add(GUIPrimitives::Text{
          .position = getPosition(),
          .size = getSize(),
          .text = m_text,
          .font = resourceManager.get<FontHandle>(m_font),
          .color = getColor()
        });
      }
  };
}

#endif
