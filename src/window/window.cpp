#include "glfw/glfw_window.hpp"
#include "window.hpp"

// C++ standard library
#include <exception>
#include <functional>
#include <memory>
#include <stdexcept>
#include <string>

// Local includes
#include "../events/event_dispatcher.hpp"
#include "../services/service_locator.hpp"
#include "keyboard_events.hpp"

namespace IronFrost {
  float IWindow::getWidth() const {
    return m_size.x;
  }

  float IWindow::getHeight() const {
    return m_size.y;
  }

  std::unique_ptr<IWindow> IWindow::create(WINDOW_LIBRARY library, int width, int height, const std::string& title) {
    if (library == WINDOW_LIBRARY::GLFW) {
      return GLFW_Window::create(width, height, title);
    }

    throw std::runtime_error("Unknown window library");
  }

  const glm::vec2& IWindow::getSize() const {
    return m_size;
  }

  IKeyboard::IKeyboard() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    eventDispatcher.registerListener<UnregisterKeyboardCallbackEvent>(
      [&](const UnregisterKeyboardCallbackEvent& event) {
        std::cout << "Unregistering keyboard callback for key: " << event.getKey() << std::endl;
        removeCallback(event.getKey());
      });
  }

  void IKeyboard::setCallback(KeyType name, std::function<void()> callback) {
    m_callbacks[name] = std::move(callback);
  }

  void IKeyboard::removeCallback(KeyType name) {
    m_callbacks.erase(name);
  }

  void IKeyboard::triggerCallback(KeyType name) {
    auto it = m_callbacks.find(name);
    if (it != m_callbacks.end()) {
      it->second();
    }
  }

  void IKeyboard::clearAllCallbacks() {
    m_callbacks.clear();
  }
}
