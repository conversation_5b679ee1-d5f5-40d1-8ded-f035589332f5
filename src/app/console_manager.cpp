#include "console_manager.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <ranges>
#include <sstream>
#include <string>

// Local includes
#include "../events/events.hpp"
#include "../gui/gui.hpp"
#include "../gui/widgets/image_widget.hpp"
#include "../gui/widgets/label_widget.hpp"
#include "../gui/widgets/panel_widget.hpp"
#include "../services/service_locator.hpp"
#include "../utils/string_id.hpp"
#include "../window/console/console.hpp"

namespace IronFrost {

  ConsoleManager::ConsoleManager(GUI& globalGUI, Console& console) 
    : m_globalGUI(globalGUI), m_console(console) {
  }

  ConsoleManager::~ConsoleManager() {
    // Unregister event listeners
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();
    eventDispatcher.unregisterListener<ConsoleOpenEvent>(m_consoleOpenHandle);
    eventDispatcher.unregisterListener<ConsoleCloseEvent>(m_consoleCloseHandle);
    eventDispatcher.unregisterListener<ConsoleInputBufferEvent>(m_consoleInputBufferHandle);
    eventDispatcher.unregisterListener<ConsoleLogEvent>(m_consoleLogHandle);
  }

  void ConsoleManager::initialize() {
    std::cout << "Initializing console manager" << '\n';
    createConsoleWidget();
    registerEventListeners();
  }

  void ConsoleManager::createConsoleWidget() {
    auto background = std::make_unique<ImageWidget>();
      background->setPosition({0, 1080});
      background->setSize({3840, 1080});
      background->setColor({0.0, 0.0, 0.0});
      background->setAlpha(0.33F);

    auto command = std::make_unique<LabelWidget>(StringID("font::console-36"), "");
      command->setPosition({20, 1100});
      command->setSize({1.0, 1.0});
      command->setColor({1.0, 1.0, 1.0});
      command->setAlpha(1.0F);

    auto log = std::make_unique<LabelWidget>(StringID("font::console-36"), "");
      log->setPosition({20, 1200});
      log->setSize({1.0, 1.0});
      log->setColor({1.0, 1.0, 1.0});
      log->setAlpha(1.0F);

    auto console = std::make_unique<PanelWidget>();
      console->setPosition({0, 0});
      console->setSize({3840, 1080});
      console->setVisible(false);

    console->addWidget(StringID("background"), std::move(background));
    console->addWidget(StringID("command"), std::move(command));
    console->addWidget(StringID("log"), std::move(log));

    m_globalGUI.addWidget(StringID("system::console"), std::move(console));
  }

  void ConsoleManager::registerEventListeners() {
    auto& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    m_consoleOpenHandle = eventDispatcher.registerListener<ConsoleOpenEvent>(
      [&](const ConsoleOpenEvent &event) {
        m_globalGUI.getWidget(StringID("system::console")).setVisible(true);
      });

    m_consoleCloseHandle = eventDispatcher.registerListener<ConsoleCloseEvent>(
      [&](const ConsoleCloseEvent &event) {
        m_globalGUI.getWidget(StringID("system::console")).setVisible(false);
      });

    m_consoleInputBufferHandle = eventDispatcher.registerListener<ConsoleInputBufferEvent>(
      [&](const ConsoleInputBufferEvent &event) { 
        if(m_console.isOpen()) {
          const PanelWidget& console = dynamic_cast<const PanelWidget&>(m_globalGUI.getWidget(StringID("system::console")));
          dynamic_cast<LabelWidget&>(console.getWidget(StringID("command"))).setText(event.getInput());
        }
      });

    m_consoleLogHandle = eventDispatcher.registerListener<ConsoleLogEvent>(
      [&](const ConsoleLogEvent &event) {
        if (m_console.isOpen()) {
          auto& console = dynamic_cast<PanelWidget&>(m_globalGUI.getWidget(StringID("system::console")));
          auto& logLabel = dynamic_cast<LabelWidget&>(console.getWidget(StringID("log")));

          std::ostringstream logStream;
          for (const std::string& val : m_console.getOutputLog() | std::views::reverse | std::views::take(20)) {
              logStream << val << '\n';
          }

          logLabel.setText(logStream.str());
        }
      });
  }

}
