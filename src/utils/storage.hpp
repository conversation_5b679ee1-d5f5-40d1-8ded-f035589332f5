#ifndef __IF__STORAGE_HPP
#define __IF__STORAGE_HPP

// C++ standard library
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <utility>
#include <vector>

namespace IronFrost {
  template<typename ID, typename OBJ>
  class Storage {
    private: 
      static inline std::atomic<ID> nextID{0};

      std::unordered_map<ID, size_t> indexMap;
      std::vector<OBJ> objects;

      mutable std::shared_mutex mutex;

      ID getNextID() {
        return nextID++;
      }

      ID findIdForIndex(size_t _index) {
        for (auto& [id, index] : indexMap) {
          if (index == _index) return id;
        }

        return ID(-1);
      }

    public:
      ID insert(const OBJ& _object) {
        std::unique_lock lock(mutex);

        ID id = getNextID();

        indexMap[id] = objects.size();
        objects.emplace_back(_object);

        return id;
      }

      void destroy(ID _id) {
        std::unique_lock lock(mutex);

        auto it = indexMap.find(_id);
        if (it == indexMap.end()) return;

        size_t indexToRemove = it->second;
        size_t lastIndex = objects.size() - 1;

        if (indexToRemove != lastIndex) {
          ID lastId = findIdForIndex(lastIndex);

          std::swap(objects[indexToRemove], objects[lastIndex]);
          indexMap[lastId] = indexToRemove;
        }

        objects.pop_back();
        indexMap.erase(_id);
      }

      OBJ* get(ID _id) {
        std::shared_lock lock(mutex);

        auto it = indexMap.find(_id);
        if (it == indexMap.end()) return nullptr;
        return &objects[it->second];
      }

      const OBJ* get(ID _id) const {
        std::shared_lock lock(mutex);

        auto it = indexMap.find(_id);
        if (it == indexMap.end()) return nullptr;
        return &objects[it->second];
      }

      void clear() {
        std::unique_lock lock(mutex);

        indexMap.clear();
        objects.clear();
      }
  };
}

#endif
